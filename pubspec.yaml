name: daleel
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # Network
  dio: ^5.7.0

  # State Management
  flutter_bloc: ^8.1.6
  equatable: ^2.0.5
  get_it: ^8.0.2
  dartz: ^0.10.1

  # Developer Tools Inspector
  flutter_screenutil: ^5.9.3
  bloc_concurrency: ^0.2.5
  awesome_dio_interceptor: ^1.3.0
  cached_network_image: ^3.4.1
  flutter_svg: ^2.0.14

  # Utils
  shared_preferences: ^2.3.3
  flutter_local_notifications: ^19.2.1
  fluttertoast: ^8.2.8
  intl: ^0.20.2
  google_fonts: ^6.2.1
  go_router: ^14.4.1
  flutter_widget_from_html: ^0.16.0
  dropdown_button2: ^2.3.9
  keyboard_dismisser: ^3.0.0
  searchable_paginated_dropdown: ^1.3.0
  expandable: ^5.0.1
  #webview_flutter: ^4.9.0

  # Webview
  flutter_inappwebview: ^6.1.5

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - locale/en.json
    - locale/ar.json
    - assets/images/onboarding/
    - assets/images/splash/

#     Images
    - assets/assets/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: DroidKufi
      fonts:
        - asset: fonts/ArbFONTS-DroidKufi-Regular.ttf
        - asset: fonts/ArbFONTS-DroidKufi-Bold.ttf
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
