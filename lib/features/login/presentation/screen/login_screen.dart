import 'dart:developer';

import 'package:daleel/app_router.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/screen/home_screen.dart';
import 'package:daleel/features/main_taps/screens/main_taps/presentation/screen/main_taps_screen.dart';
import 'package:daleel/features/uaepass/UaePassService.dart';
import 'package:daleel/features/widgets/app_buttons/app_elevated_button.dart';
import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:daleel/apis/_base/dio_api_manager.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/core/widgets/base_stateful_screen_widget.dart';
import 'package:daleel/features/login/data/datasources/login_api_manager.dart';
import 'package:daleel/features/login/data/datasources/login_local_datasources.dart';
import 'package:daleel/features/login/data/models/login_response_api_model.dart';
import 'package:daleel/features/login/data/models/login_send_api_model.dart';
import 'package:daleel/features/login/data/repository/login_repository.dart';
import 'package:daleel/features/login/domain/usecases/login_use_case.dart';
import 'package:daleel/features/login/domain/usecases/login_validate_use_case.dart';
import 'package:daleel/features/login/domain/usecases/save_user_info_usecase.dart';
import 'package:daleel/features/login/domain/usecases/set_user_logged_in_usecase.dart';
import 'package:daleel/features/login/presentation/bloc/login_bloc.dart';
import 'package:daleel/features/widgets/text_field/app_text_form_filed_widget.dart';
import 'package:daleel/logic/user_manager.dart';
import 'package:daleel/preferences/preferences_manager.dart';
import 'package:daleel/utils/feedback/feedback_message.dart';
import 'package:daleel/utils/validations/app_validate.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/widgets/DynamicWebView.dart';
import '../../../../res/app_colors.dart';

class LoginScreen extends StatelessWidget {
  LoginScreen({super.key});

  static const String routeName = '/login';

  final PreferencesManager preferencesManager = GetIt.I<PreferencesManager>();
  final DioApiManager dioApiManager = GetIt.I<DioApiManager>();

  @override
  Widget build(BuildContext context) {
    LoginRepository loginRepository = LoginRepository(
      LoginLocaleManager(preferencesManager),
      LoginApiManager(dioApiManager),
    );
    return BlocProvider<LoginBloc>(
      create:
          (BuildContext context) => LoginBloc(
            loginUseCase: LoginUseCase(loginRepository),
            loginValidateUseCase: LoginValidateUseCase(),
            saveUserInfoUseCase: SaveUserInfoUseCase(loginRepository),
            setUserLoggedInUseCase: SetUserLoggedInUseCase(loginRepository),
          ),
      child: const LoginScreenWithBloc(),
    );
  }
}

class LoginScreenWithBloc extends BaseStatefulScreenWidget {
  const LoginScreenWithBloc({super.key});

  @override
  BaseScreenState<LoginScreenWithBloc> baseScreenCreateState() =>
      _LoginScreenWithBlocState();
}

class _LoginScreenWithBlocState extends BaseScreenState<LoginScreenWithBloc>
    with AppValidate {
  AutovalidateMode _autoValidateMode = AutovalidateMode.disabled;
  UserMangers user = GetIt.I<UserMangers>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool mobileSelected = false;
  TextEditingController passwordController = TextEditingController();
  TextEditingController userNameController = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget baseScreenBuild(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<LoginBloc, LoginState>(
        listener: (context, state) async {
          if (state is LoginLoadingState) {
            showLoading();
          } else {
            hideLoading();
          }
          if (state is LoginNotValidatedState) {
            _autoValidateMode = AutovalidateMode.onUserInteraction;
          } else if (state is LoginValidatedState) {
            _loginEventApi();
          } else if (state is LoginSuccessfullyState) {
            _saveUserInfoEvent(state.loginResponseModel);
          } else if (state is LoginErrorState) {
            showFeedbackMessage(
              state.isLocalizationKey
                  ? context.translate(state.errorMassage)
                  : state.errorMassage,
            );
          } else if (state is SavedUserInfoState) {
            _setLoggedInUserEvent();
          } else if (state is SetLoggedInUserState) {
            AppRouter.mainNavigatorKey.currentContext?.go(
              MainTapsScreen.routeName,
            );
          }
        },
        builder: (context, state) {
          return buildLoginWidget();
        },
      ),
    );
  }

  Widget buildLoginWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      child: Form(
        key: _formKey,
        autovalidateMode: _autoValidateMode,
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              SizedBox(height: 70.h),
              Center(
                child: Image.asset(AppAssetPaths.daleel_splash, height: 96),
              ),
              SizedBox(height: 20.h),
              Center(
                child: Text(
                  context.translate(LocalizationKeys.login),
                  style: TextStyle(
                    fontSize: 24,
                    color: AppColors.buttonBlackTextColor,
                    fontWeight: FontWeight.w700,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(height: 40.h),
              _emailWidget(),
              SizedBox(height: 24.h),
              _passwordWidget(),
              SizedBox(height: 14.h),
              Row(
                children: [
                  Text(
                    context.translate(LocalizationKeys.forgotPassword),
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: AppColors.forgetPassTitle,
                    ),
                  ),
                  const SizedBox(width: 4),
                  TextButton(
                    style: ButtonStyle(
                      foregroundColor: WidgetStateProperty.all(
                        AppColors.buttonBackground,
                      ),
                      textStyle: WidgetStateProperty.all(
                        const TextStyle(
                          fontWeight: FontWeight.w700,
                          fontSize: 14,
                        ),
                      ),
                      padding: WidgetStateProperty.all(EdgeInsets.zero),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      minimumSize: WidgetStateProperty.all(Size.zero),
                    ),
                    onPressed: () {},
                    child: Text(
                      context.translate(LocalizationKeys.clickToReset),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 24.h),
              SizedBox(
                height: 50.h,
                width: double.infinity,
                child: AppElevatedButton.withTitle(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  onPressed: _getStartedClicked,
                  title: context.translate(LocalizationKeys.login),
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                ),
              ),
              SizedBox(height: 24.h),
              Center(
                child: Text(
                  context.translate(
                    LocalizationKeys.dontHaveAccount,
                  ), //'Don’t have an account?',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColors.headlineSmall,
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(height: 24.h),
              SizedBox(
                height: 50.h,
                width: double.infinity,
                child: AppElevatedButton.withTitle(
                  shape: RoundedRectangleBorder(
                    side: BorderSide(
                      color: AppColors.colorSecondary,
                      width: 1.5,
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  onPressed: () {
                    context.push(
                      DynamicWebView.routeName,
                      extra: {
                        'initialUrl': 'https://daleel-spea-front-chdda7awapavdecy.eastus-01.azurewebsites.net/auth/login/registration',
                        'successRedirectUrl': 'https://daleel-spea-front-qa-cpcscfarfndddcbk.eastus-01.azurewebsites.net/signup-success',
                        'onSuccessCallback': () {
                          log("success", name: "SignupLogger");
                          context.push(LoginScreen.routeName);
                        },
                        'onFailureCallback': () {
                          log("failed", name: "SignupLogger");
                        },
                        'appBarTitle': context.translate(
                          LocalizationKeys.createAccount,
                        ),
                      },
                    );
                  },
                  color: Colors.transparent,
                  textColor: AppColors.colorSecondary,
                  title: context.translate(LocalizationKeys.createAccount),
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                ),
              ),
              SizedBox(height: 24.h),
              Center(
                child: Text(
                  context.translate(LocalizationKeys.orThrough),
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColors.headlineSmall,
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(height: 24.h),
              SizedBox(
                height: 50.h,
                width: double.infinity,
                child: AppElevatedButton.withTitleAndIcon(
                  onPressed: () async{
                    try{
                      await UaePassService.instance.signIn();
                    }catch(e){
                      print("UAE PASS login failed: $e");
                    }
                  },
                  title: context.translate(LocalizationKeys.uaePassRegister),
                  icon: Icon(Icons.fingerprint, color: AppColors.whiteIcon),
                ),
              ),
              SizedBox(height: 24.h),
              Center(child: Image.asset(AppAssetPaths.spea_splash, height: 64)),
              SizedBox(height: 40.h),
              Center(
                child: Text(
                  context.translate(LocalizationKeys.copyrightText),
                  style: TextStyle(
                    fontSize: 10,
                    color: AppColors.headlineSmall,
                    fontWeight: FontWeight.w400,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _emailWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              context.translate(LocalizationKeys.userName),
              style: TextStyle(
                fontSize: 16,
                color: AppColors.buttonBlackTextColor,
                fontWeight: FontWeight.w400,
              ),
            ),
            Text(
              ' *',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.formFieldAsterisk,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        AppTextFormField(
          hintText: context.translate(LocalizationKeys.userNameHint),
          fillColor: Colors.transparent,
          hintTextStyle: TextStyle(
            fontSize: 16,
            color: AppColors.formFieldHintText,
            fontWeight: FontWeight.w400,
          ),
          controller: userNameController,
          validator:
              (value) =>
                  value?.isEmpty == true
                      ? context.translate(LocalizationKeys.usernameRequired)
                      : null,
          enableBorderColor: AppColors.formFieldBorder,
        ),
      ],
    );
  }

  Widget _passwordWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              context.translate(LocalizationKeys.password),
              style: TextStyle(
                fontSize: 16,
                color: AppColors.buttonBlackTextColor,
                fontWeight: FontWeight.w400,
              ),
            ),
            Text(
              ' *',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.formFieldAsterisk,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        AppTextFormField(
          hintText: context.translate(LocalizationKeys.passwordHint),
          fillColor: Colors.transparent,
          hintTextStyle: TextStyle(
            fontSize: 16,
            color: AppColors.formFieldHintText,
            fontWeight: FontWeight.w400,
          ),
          controller: passwordController,
          validator: passwordValidator,
          enableBorderColor: AppColors.formFieldBorder,
          obscure: true,
        ),
      ],
    );
  }

  ///////////////////////////////////////////////////////////
  /////////////////// Helper methods ////////////////////////
  ///////////////////////////////////////////////////////////

  LoginBloc get currentBloc => context.read<LoginBloc>();

  void _getStartedClicked() {
    FocusManager.instance.primaryFocus?.unfocus();
    currentBloc.add(ValidateLoginEvent(_formKey));
  }

  void _loginEventApi() {
    String userName = userNameController.text;
    currentBloc.add(
      LoginApiEvent(
        LoginSendModel(userName: userName, password: passwordController.text),
      ),
    );
  }

  void _saveUserInfoEvent(LoginResponseApiModel loginResponseModel) {
    currentBloc.add(SaveUserInfoEvent(loginResponseModel));
  }

  void _setLoggedInUserEvent() {
    currentBloc.add(const SetLoggedInUserEvent());
  }
}
