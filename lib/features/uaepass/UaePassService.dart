import 'package:flutter_uae_pass/flutter_uae_pass.dart';

class UaePassService {
  UaePassService._();
  static final UaePassService instance = UaePassService._();

  final _uaePass = UaePass();

  Future<void> initSandbox() async {
    await _uaePass.setUpSandbox(language: "ar");
  }

  Future<void> initProduction() async {
    await _uaePass.setUpEnvironment(
      clientId: "<client_id>",
      clientSecret: "<client_secret>",
      urlScheme: "<url_scheme>",
      redirectUri: "<redirect_uri>",
      isProduction: true,
      language: "ar",
    );
  }

  Future<void> signIn() async {
    final authCode = await _uaePass.signIn();
    final accessToken = await _uaePass.getAccessToken(authCode);
    final profile = await _uaePass.getProfile(accessToken);
    print("accessToken: $accessToken");
    print("profile: ${profile?.fullnameEN}");
  }

  Future<void> signOut() async {
    try {
      await _uaePass.signOut();
    } catch (_) {}
  }
}
