
import 'dart:collection';
import 'dart:convert';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class WebViewAuthScripts {
  WebViewAuthScripts._();

  static String bootstrapAuthJs({
    required String? token,
    required String? language,
    Map<String, dynamic>? claims,
    Map<String, dynamic>? user,
    String platform = 'mobileAndroid',
  }) {
    final claimsJson = jsonEncode(
      claims ?? const {"S_MenuItem_user": "S_MenuItem_user"},
    );
    final userJson = jsonEncode(
      user ?? const {"id": 123, "name": "<PERSON>"},
    );

    return '''
(function() {
  try {
    const storageData = {
      "\$AJ\$token": "${token ?? ''}",
      "preferredLanguage": "${language ?? 'en'}",
      "\$AJ\$claims": JSON.stringify($claimsJson),
      "\$AJ\$user": JSON.stringify($userJson),
      "platform": "$platform"
    };

    localStorage.clear();
    sessionStorage.clear();

    for (const key in storageData) {
      localStorage.setItem(key, storageData[key]);
    }
  } catch (e) {
    console.error("Auth bootstrap error:", e);
  }
})();
''';
  }

  static UnmodifiableListView<UserScript> buildInitialUserScripts({
    required String? token,
    required String? language,
    Map<String, dynamic>? claims,
    Map<String, dynamic>? user,
    String platform = 'mobileAndroid',
    UserScriptInjectionTime injectionTime =
        UserScriptInjectionTime.AT_DOCUMENT_START,
  }) {
    return UnmodifiableListView<UserScript>([
      UserScript(
        source: bootstrapAuthJs(
          token: token,
          language: language,
          claims: claims,
          user: user,
          platform: platform,
        ),
        injectionTime: injectionTime,
      ),
    ]);
  }
}
 