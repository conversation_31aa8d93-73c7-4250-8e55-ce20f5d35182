import 'package:daleel/apis/api_urls.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/webview/dynamic_web_view.dart';
import 'package:daleel/features/widgets/webview/app_webview_widget.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
CertificatesScreen

class TestWebView extends StatefulWidget {
  const TestWebView({super.key});

  static const routeName = "/test_web_view";

  @override
  State<TestWebView> createState() => _TestWebViewState();
}

class _TestWebViewState extends State<TestWebView> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: DynamicWebView(
        url:
        "https://daleel-spea-front-qa-cpcscfarfndddcbk.eastus-01.azurewebsites.net/requests-list",
        redirectUrl:
        "https://daleel-spea-front-qa-cpcscfarfndddcbk.eastus-01.azurewebsites.net/signup-success",
        onSuccessCallback: () {
          debugPrint("Auth Success ");
        },
        onFailureCallback: () {
          debugPrint("Auth Failed ");
        },
      ),
    );
  }
}