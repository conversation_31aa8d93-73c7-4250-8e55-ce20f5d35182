import 'package:daleel/apis/api_urls.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/webview/dynamic_web_view.dart';
import 'package:daleel/features/widgets/page_header/page_header.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';

class CertificatesScreen extends StatefulWidget {
  const CertificatesScreen({super.key});

  static const routeName = "/test_web_view";

  @override
  State<CertificatesScreen> createState() => _TestWebViewState();
}

class _TestWebViewState extends State<CertificatesScreen> {
  @override
  Widget build(BuildContext context) {
    final String webViewUrl = ApiUrls.baseWebViewUrl + ApiUrls.askCertificates;

    return Scaffold(
      appBar: PageHeader(
        title: context.translate(LocalizationKeys.certificates),
      ),
      body: DynamicWebView(
        url: webViewUrl,
        redirectUrl: webViewUrl,
        onSuccessCallback: () {
          debugPrint("Certificates Success ");
        },
        onFailureCallback: () {
          debugPrint("Cartificates Failed ");
        },
      ),
    );
  }
}
