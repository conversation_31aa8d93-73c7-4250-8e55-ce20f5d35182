import 'package:daleel/apis/api_urls.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/widgets/webview/app_webview_widget.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';

class ChatsScreen extends StatelessWidget {
  const ChatsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final String webViewUrl = ApiUrls.baseWebViewUrl + ApiUrls.messages;

    return AppWebViewWidget(
      url: webViewUrl,
      title: context.translate(LocalizationKeys.chatsTab),
      showHeader: true,
    );
  }
}
