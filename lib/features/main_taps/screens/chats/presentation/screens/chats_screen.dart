import 'package:daleel/apis/api_urls.dart';
import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/webview/dynamic_web_view.dart';
import 'package:daleel/features/widgets/page_header/page_header.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';

class ChatsScreen extends StatefulWidget {
  const ChatsScreen({super.key});

  @override
  State<ChatsScreen> createState() => _ChatsScreenState();
}

class _ChatsScreenState extends State<ChatsScreen> {
  bool _isWebViewLoading = true;

  @override
  Widget build(BuildContext context) {
    final String webViewUrl = ApiUrls.baseWebViewUrl + ApiUrls.messages;

    return Scaffold(
      appBar: PageHeader(title: context.translate(LocalizationKeys.chatsTab)),
      body: Stack(
        children: [
          DynamicWebView(
            url: webViewUrl,
            redirectUrl: webViewUrl,
            onLoadStart: () {
              if (mounted) {
                setState(() {
                  _isWebViewLoading = true;
                });
              }
            },
            onLoadStop: () {
              if (mounted) {
                setState(() {
                  _isWebViewLoading = false;
                });
              }
            },
            onSuccessCallback: () {
              if (mounted) {
                setState(() {
                  _isWebViewLoading = false;
                });
              }
            },
            onFailureCallback: () {
              if (mounted) {
                setState(() {
                  _isWebViewLoading = false;
                });
              }
            },
          ),
          if (_isWebViewLoading)
            Container(
              color: AppColors.scaffoldBackground,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(color: AppColors.colorSecondary),
                    const SizedBox(height: 16),
                    Text(
                      context.translate(LocalizationKeys.plzWait),
                      style: TextStyle(
                        fontSize: 16,
                        color: AppColors.colorSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
