import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/children-categorization/presentation/registered-children/presentation/screen/registered_children_screen.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/bloc/home_bloc.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/widgets/home_stat_card.dart';
import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../../children-categorization/presentation/graduated-children/screen/graduated_children_screen.dart';
import '../../../../../children-categorization/presentation/unregistered-children/screen/unregistered_children_screen.dart';
import '../../../../../children-categorization/presentation/withdrawn-children/screen/withdrawn-children_screen.dart';

class HomeStatsSection extends StatelessWidget {
  final HomeState state;

  const HomeStatsSection({super.key, required this.state});

  @override
  Widget build(BuildContext context) {
    if (state is HomeLoadedState) {
      return Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                HomeStatCard(
                  color: AppColors.colorHomeGreenButton,
                  text: context.translate(LocalizationKeys.registeredChildren),
                  imagePath: AppAssetPaths.ic_Open_enrollment,
                  textColor: AppColors.whiteIcon,
                  onTap: (){
                    context.push(RegisteredChildrenScreen.routeName);
                  },
                ),
                SizedBox(width: 20),
                HomeStatCard(
                  color: AppColors.colorHomeButtons.withValues(alpha: 0.2),
                  text: context.translate(
                    LocalizationKeys.unregisteredChildren,
                  ),
                  textColor: AppColors.colorHomeButtons,
                  imagePath: AppAssetPaths.ic_No_id_card,
                  onTap: (){
                    context.push(UnRegisteredChildrenScreen.routeName);
                  },
                ),
              ],
            ),
          ),
          SizedBox(height: 20.h),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                HomeStatCard(
                  color: AppColors.colorHomeButtons.withValues(alpha: 0.2),
                  text: context.translate(LocalizationKeys.sonsWithdrawn),
                  textColor: AppColors.colorHomeButtons,
                  imagePath: AppAssetPaths.ic_Unsubscribe,
                  onTap: (){
                    context.push(WithdrawnChildrenScreen.routeName);
                  },
                ),
                SizedBox(width: 20),
                HomeStatCard(
                  color: AppColors.colorHomeButtons.withValues(alpha: 0.2),
                  text: context.translate(
                    LocalizationKeys.sonsFinishedStudying,
                  ),
                  textColor: AppColors.colorHomeButtons,
                  imagePath: AppAssetPaths.ic_finish,
                  onTap: (){
                    context.push(GraduatedChildrenScreen.routeName);
                  },
                ),
              ],
            ),
          ),
        ],
      );
    }
    return const SizedBox.shrink();
  }
}
