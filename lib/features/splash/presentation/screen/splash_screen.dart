import 'dart:async';
import 'package:daleel/features/main_taps/screens/main_taps/presentation/screen/main_taps_screen.dart';
import 'package:daleel/features/splash/presentation/widgets/splash_widget.dart';
import 'package:daleel/features/login/presentation/screen/login_screen.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:daleel/preferences/preferences_manager.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/status_bar/statusbar_controller.dart';

class SplashScreen extends StatefulWidget {
  static const routeName = "/splash";
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  var preferencesManager = GetIt.I<PreferencesManager>();

  @override
  void initState() {
    super.initState();
    setStatusBarColor(
      color: AppColors.transparentColor,
      brightness: Brightness.dark,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(children: <Widget>[_backgroundWidget(), SplashWidget()]),
    );
  }

  Widget _backgroundWidget() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppColors.splashBackground,
    );
  }
}
