import 'package:daleel/features/main_taps/screens/home/<USER>/screen/home_screen.dart';
import 'package:daleel/res/app_asset_paths.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import '../../../../preferences/preferences_manager.dart';
import '../../../login/presentation/screen/login_screen.dart';
import '../../../onboarding/presentation/screens/onboarding_screen.dart';

class SplashWidget extends StatefulWidget {
  const SplashWidget({super.key});

  @override
  State<SplashWidget> createState() => _SplashWidgetState();
}

class _SplashWidgetState extends State<SplashWidget> with TickerProviderStateMixin {
  static const _initialDelay = Duration(milliseconds: 200);
  static const _imageSwitchDelay = Duration(seconds: 3);
  static const _fadeDuration = Duration(milliseconds: 700);
  final preferencesManager = GetIt.I<PreferencesManager>();

  late final AnimationController _firstImageController;
  late final AnimationController _secondImageController;
  late final Animation<double> _firstFadeAnimation;
  late final Animation<double> _secondFadeAnimation;

  @override
  void initState() {
    super.initState();

    _firstImageController = AnimationController(
      vsync: this,
      duration: _fadeDuration,
    );
    _secondImageController = AnimationController(
      vsync: this,
      duration: _fadeDuration,
    );

    _firstFadeAnimation = CurvedAnimation(
      parent: _firstImageController,
      curve: Curves.easeInOut,
    );

    _secondFadeAnimation = CurvedAnimation(
      parent: _secondImageController,
      curve: Curves.easeInOut,
    );

    _startAnimations();
  }

  Future<void> _startAnimations() async {
    await Future.delayed(_initialDelay);
    if (!mounted) return;
    await _firstImageController.forward();

    await Future.delayed(_imageSwitchDelay);
    if (!mounted) return;
    await _firstImageController.reverse();

    await Future.delayed(_fadeDuration);
    if (!mounted) return;
    await _secondImageController.forward();

    await Future.delayed(_imageSwitchDelay);
    if (!mounted) return;

    final isOnBoardingShown = await preferencesManager.isOnBoardingShown();

    if (isOnBoardingShown) {
      final loggedIn = await preferencesManager.isLoggedIn();
      if (loggedIn) {
        context.go(HomeScreen.routeName);
      } else {
        context.go(LoginScreen.routeName);
      }
    } else {
      context.go(OnboardingScreen.routeName);
    }
  }


  @override
  void dispose() {
    _firstImageController.dispose();
    _secondImageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Stack(
          alignment: Alignment.center,
          children: [
            FadeTransition(
              opacity: _firstFadeAnimation,
              child: Image.asset(
                AppAssetPaths.imgSpeaSplash,
                fit: BoxFit.contain,
              ),
            ),
            FadeTransition(
              opacity: _secondFadeAnimation,
              child: Image.asset(
                AppAssetPaths.imgDaleelSplash,
                fit: BoxFit.contain,
              ),
            ),
          ],
        ),
      ),
    );
  }
}


