import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import '../../../../../apis/_base/dio_api_manager.dart';
import '../../../../../logic/user_manager.dart';
import '../../../../../res/app_asset_paths.dart';
import '../../../../../res/app_colors.dart';
import '../../../../../utils/locale/app_localization_keys.dart';
import '../../../data/datasources/children_categorization_api_manager.dart';
import '../../../data/models/children_categorization_send_model.dart';
import '../../../data/repository/children_categorization_repository.dart';
import '../../../domain/usecase/children_categorization_use_case.dart';
import '../../bloc/children_categorization_bloc.dart';
import '../widget/withdrawn_card.dart';


class WithdrawnChildrenScreen extends StatelessWidget {
  const WithdrawnChildrenScreen({super.key});

  static const routeName = "/withdrawn_children";

  @override
  Widget build(BuildContext context) {
    final dioApiManager = GetIt.I<DioApiManager>();
    final guardianId = GetIt.I<UserMangers>().getGuardianId();
    final schoolYearId = GetIt.I<UserMangers>().getSchoolYearId();
    final registeredChildrenRepository = RegisteredChildrenRepository(
      ChildrenCategorizationApiManager(dioApiManager),
    );

    return BlocProvider<ChildrenCategorizationBloc>(
      create: (_) => ChildrenCategorizationBloc(
        ChildrenCategorizationUseCase(registeredChildrenRepository),
      )..add(
        GetRegisteredChildrenEvent(
          ChildrenCategorizationSendModel(guardianId:guardianId!, schoolYearId: schoolYearId!),
        ),
      ),
      child: const RegisteredChildrenScreenWithBloc(),
    );
  }
}

class RegisteredChildrenScreenWithBloc extends StatefulWidget {
  const RegisteredChildrenScreenWithBloc({super.key});

  @override
  State<RegisteredChildrenScreenWithBloc> createState() =>
      _RegisteredChildrenScreenWithBlocState();
}

class _RegisteredChildrenScreenWithBlocState extends State<RegisteredChildrenScreenWithBloc> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: BlocConsumer<ChildrenCategorizationBloc, RegisteredChildrenState>(
        listener: (context, state) {
          if (state is RegisteredChildrenError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          }
        },
        builder: (context, state) {
          return SingleChildScrollView(
            child: Column(
              children: [
                _buildHeader(),
                if (state is RegisteredChildrenLoading) ...[
                  const SizedBox(height: 50),
                  const CircularProgressIndicator(),
                ] else if (state is RegisteredChildrenLoaded) ...[
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: state.studentsWithdrawal.length,
                    itemBuilder: (context, index) {
                      final student = state.studentsWithdrawal[index];
                      return WithDrawnCard(student: student);
                    },
                  ),
                ] else if (state is RegisteredChildrenError) ...[
                  const SizedBox(height: 20),
                  Text(
                    state.message,
                    style: const TextStyle(color: Colors.red),
                  ),
                ]
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return SizedBox(
      height: 200,
      width: double.infinity,
      child: Stack(
        children: [
          Align(
            alignment: Alignment.centerLeft,
            child: Image.asset(
              AppAssetPaths.imWithDrawChildren,
              height: 200,
              width: 200,
              fit: BoxFit.cover,
            ),
          ),
          Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.5),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Icon(
                        Icons.arrow_back_ios_new,
                        color: AppColors.colorHomeButtons,
                        size: 20,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 50),
                Text(
                  context.translate(LocalizationKeys.sonsWithdrawn),
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

