import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/logic/user_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get_it/get_it.dart';
import 'dart:convert';

class DynamicWebView extends StatefulWidget {
  final String initialUrl;
  final String successRedirectUrl;
  final Function? onSuccessCallback;
  final Function? onFailureCallback;
  final String appBarTitle;

  const DynamicWebView({
    required this.initialUrl,
    required this.successRedirectUrl,
    this.onSuccessCallback,
    this.onFailureCallback,
    required this.appBarTitle,
    Key? key,
  }) : super(key: key);

  static const String routeName = '/webview';

  @override
  State<DynamicWebView> createState() => _DynamicWebViewState();
}

class _DynamicWebViewState extends State<DynamicWebView> {
  late InAppWebViewController _webViewController;
  late final String token;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    token = GetIt.I<UserMangers>().token ?? '';
    print("Token: $token");
  }
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(title: Text(widget.appBarTitle)),
        body: InAppWebView(
          initialUrlRequest: URLRequest(url: WebUri(widget.initialUrl)),
          initialOptions: InAppWebViewGroupOptions(
            crossPlatform: InAppWebViewOptions(
              javaScriptEnabled: true,
              mediaPlaybackRequiresUserGesture: false,
            ),
            android: AndroidInAppWebViewOptions(
              useHybridComposition: true,
              allowFileAccess: true,
            ),
          ),
          onWebViewCreated: (controller) {
            _webViewController = controller;
          },
          onLoadStart: (controller, url) {
          },
          onLoadStop: (controller, url) async {
            if (url?.toString() == widget.successRedirectUrl) {
              widget.onSuccessCallback?.call();
              return;
            }

            final data = {"token": token, "lang": context.languageKey};
            await controller.evaluateJavascript(
              source: "window.postMessage(${jsonEncode(data)}, '*');",
            );
          },
          onLoadResource: (controller, resource) {
            final url = resource.url.toString();
            if (url.contains("signup-success")) {
              widget.onSuccessCallback?.call();
            } else if (url.contains("signup-failure")) {
              widget.onFailureCallback?.call();
            }
          },
          onLoadError: (controller, url, code, message) {
            widget.onFailureCallback?.call();
          },
          onLoadHttpError: (controller, url, statusCode, description) {
            widget.onFailureCallback?.call();
          },
          onConsoleMessage: (controller, consoleMessage) {
            debugPrint(
              "Console: ${consoleMessage.messageLevel} - ${consoleMessage.message}",
            );
          },
        ),
      ),
    );
  }
}
