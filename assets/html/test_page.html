<!DOCTYPE html>
<html>
<head>
    <title>WebView Test</title>
    <script>
        window.onload = function () {
          window.addEventListener('message', function (event) {
            console.log(" Received message: ", event.data);
            document.getElementById("msg").innerText = JSON.stringify(event.data);
          });
        };

        function triggerSuccess() {
          window.location.href = "https://example.com/signup-success";
        }

        function triggerFailure() {
          window.location.href = "https://example.com/signup-failure";
        }
    </script>
</head>
<body>
<h1> WebView Test Page</h1>
<p id="msg">Waiting for token & lang...</p>
<button onclick="triggerSuccess()">Simulate Success</button>
<button onclick="triggerFailure()">Simulate Failure</button>
</body>
</html>

